package com.wormhole.hotelds.core.model.entity;

/**
 * 通话交互日志表 字段枚举
 *
 * <AUTHOR>
 * @date 2025-04-14 14:05:49
 */
public enum HdsCallLogFieldEnum {
    /**
     * 主键ID
     */
    id,

    /**
     * 关联的聊天消息ID
     */
    message_id,

    /**
     * 消息类型user、assistant、system
     */
    message_type,

    /**
     * 会话ID
     */
    conversation_id,

    /**
     * RTC语音房间ID
     */
    rtc_room_id,

    /**
     * 设备标识
     */
    device_id,

    /**
     * 客户端请求ID
     */
    client_req_id,

    /**
     * 智能体code
     */
    bot_code,

    /**
     * 消息内容
     */
    content,

    /**
     * 通话类型: 0-智能体通话 1-人工点对点通话
     */
    call_type,

    /**
     * 服务类型: SERVICE-服务工单 INQUIRY-问询
     */
    service_type,

    /**
     * 服务分类: DELIVERY-送物 CLEANING-清洁 TOURISM-周边旅游等
     */
    service_category,

    /**
     * 服务子分类
     */
    service_subcategory,

    /**
     * 服务类别名称
     */
    service_category_name,

    /**
     * 服务子类别名称
     */
    service_subcategory_name,

    /**
     * 关联工单编号
     */
    related_ticket_no,

    /**
     * 酒店代码
     */
    hotel_code,

    /**
     * 房间编码
     */
    position_code,

    /**
     * 创建时间
     */
    created_at,

    /**
     * 创建人ID
     */
    created_by,

    /**
     * 创建人姓名
     */
    created_by_name,

    /**
     * 修改时间
     */
    updated_at,

    /**
     * 最后更新人ID
     */
    updated_by,

    /**
     * 最后更新人姓名
     */
    updated_by_name,

    /**
     * 行状态: 0-已删除 1-正常
     */
    row_status;

}