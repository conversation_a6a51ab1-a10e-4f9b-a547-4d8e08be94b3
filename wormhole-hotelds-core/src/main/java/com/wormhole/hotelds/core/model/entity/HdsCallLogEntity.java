package com.wormhole.hotelds.core.model.entity;

import com.wormhole.common.model.entity.*;
import lombok.*;
import lombok.experimental.*;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.*;

import java.io.*;

/**
 * 通话交互日志表
 *
 * <AUTHOR>
 */
@Data
@Table("hds_call_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HdsCallLogEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    private Long id;


    /**
     * 关联的聊天消息ID
     */
    @Column("message_id")
    private String messageId;


    /**
     * 消息类型user、assistant、system
     */
    @Column("message_type")
    private String messageType;


    /**
     * 会话ID
     */
    @Column("conversation_id")
    private String conversationId;


    /**
     * RTC语音房间ID
     */
    @Column("rtc_room_id")
    private String rtcRoomId;


    /**
     * 设备标识
     */
    @Column("device_id")
    private String deviceId;


    /**
     * 客户端请求ID
     */
    @Column("client_req_id")
    private String clientReqId;


    /**
     * 智能体code
     */
    @Column("bot_code")
    private String botCode;


    /**
     * 消息内容
     */
    @Column("content")
    private String content;

    /**
     * 通话类型: 0-智能体通话 1-人工点对点通话
     */
    @Column("call_type")
    private Integer callType;


    /**
     * 服务类型: SERVICE-服务工单 INQUIRY-问询
     */
    @Column("service_type")
    private String serviceType;


    /**
     * 服务分类: DELIVERY-送物 CLEANING-清洁 TOURISM-周边旅游等
     */
    @Column("service_category")
    private String serviceCategory;


    /**
     * 服务子分类
     */
    @Column("service_subcategory")
    private String serviceSubcategory;


    /**
     * 服务类别名称
     */
    @Column("service_category_name")
    private String serviceCategoryName;


    /**
     * 服务子类别名称
     */
    @Column("service_subcategory_name")
    private String serviceSubcategoryName;


    /**
     * 关联工单编号
     */
    @Column("related_ticket_no")
    private String relatedTicketNo;


    /**
     * 酒店代码
     */
    @Column("hotel_code")
    private String hotelCode;


    /**
     * 房间编码
     */
    @Column("position_code")
    private String positionCode;


}