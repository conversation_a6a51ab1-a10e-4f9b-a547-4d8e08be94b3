package com.wormhole.hotelds.api.hotel.web.controller.toC;

import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.hotelds.api.hotel.web.model.req.PageCallLogQO;
import com.wormhole.hotelds.api.hotel.web.model.req.SimpleCallLogReq;
import com.wormhole.hotelds.api.hotel.web.service.CallLogService;
import com.wormhole.hotelds.api.hotel.web.model.res.HdsCallLogVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@RestController
@RequestMapping("/call_log")
public class CallLogController {

    @Autowired
    private CallLogService callLogService;

    @PostMapping("/page")
    public Mono<Result<PageResult<HdsCallLogVO>>> page(@RequestBody PageCallLogQO pageCallLogQO){
       return callLogService.page(pageCallLogQO).flatMap(Result::success);
    }
    @PostMapping("/get_simple_call_log_json")
    public Mono<Result<String>> getSimpleCallLogJson(@RequestBody SimpleCallLogReq simpleCallLogReq) {
        return callLogService.getSimpleCallLogJson(simpleCallLogReq).flatMap(Result::success);
    }
}
