package com.wormhole.hotelds.api.hotel.web.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.*;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;
import com.wormhole.agent.core.model.entity.UserEntity;
import com.wormhole.common.constant.ClientType;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.constant.*;
import com.wormhole.hotelds.api.hotel.resp.BlankResp;
import com.wormhole.hotelds.api.hotel.util.DateUtils;
import com.wormhole.hotelds.api.hotel.web.dao.HdsCallLogDao;
import com.wormhole.hotelds.api.hotel.web.model.req.*;
import com.wormhole.hotelds.api.hotel.web.dao.HdsDeviceDao;
import com.wormhole.hotelds.api.hotel.web.dao.PositionWithDeviceDao;
import com.wormhole.hotelds.api.hotel.util.DeviceUtils;
import com.wormhole.hotelds.api.hotel.web.dao.HdsCallFeedbackDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsServiceTicketDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsTicketLogsDao;
import com.wormhole.hotelds.api.hotel.req.TicketAdminPageReq;
import com.wormhole.hotelds.api.hotel.req.TicketInfoReq;
import com.wormhole.hotelds.api.hotel.req.TicketStatisticsReq;
import com.wormhole.hotelds.api.hotel.resp.TicketAdminListResp;
import com.wormhole.hotelds.api.hotel.resp.TicketDetailResp;
import com.wormhole.hotelds.api.hotel.resp.TicketLogResp;
import com.wormhole.hotelds.api.hotel.resp.TicketStatisticsResp;
import com.wormhole.hotelds.api.hotel.web.dao.HdsChatUserDao;
import com.wormhole.hotelds.api.hotel.web.dao.HdsHotelInfoDao;
import com.wormhole.hotelds.api.hotel.web.model.res.*;
import com.wormhole.hotelds.core.enums.*;
import com.wormhole.hotelds.core.model.entity.*;
import com.wormhole.hotelds.plugin.model.dto.TicketStatisticsDTO;
import jakarta.annotation.*;
import org.apache.commons.collections4.*;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.*;
import java.util.*;
import java.util.stream.Collectors;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2025/4/8
 */
@Service
@Slf4j
public class TicketAdminService {

    @Autowired
    private HdsServiceTicketDao hdsServiceTicketDao;

    @Autowired
    private PositionWithDeviceDao positionWithDeviceDao;

    @Autowired
    private HdsTicketLogsDao hdsTicketLogsDao;

    @Autowired
    private HdsDeviceDao hdsDeviceDao;

    @Autowired
    private HdsChatUserDao hdsChatUserDao;

    @Autowired
    private ServiceTicketService serviceTicketService;

    @Autowired
    private HdsCallLogDao hdsCallLogDao;

    @Autowired
    private HdsCallFeedbackDao hdsCallFeedbackDao;

    @Autowired
    private HdsHotelInfoDao hdsHotelInfoDao;

    @Autowired
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    public Mono<List<BlankResp>> getServiceTicketCategories() {
        List<BlankResp> res = new ArrayList<>();
        for (ServiceCategory value : ServiceCategory.values()) {
            BlankResp resp = new BlankResp();
            resp.setCode(value.getCode());
            resp.setName(value.getChineseName());
            res.add(resp);
        }
        return Mono.just(res);
    }

    public Mono<PageResult<TicketAdminListResp>> getTicketPage(TicketAdminPageReq req) {
        return positionWithDeviceDao.getHdsDevicePositions(req.getHotelCode(), req.getPositionName(), null)
                .map(list -> {
                    TicketShowStatusEnum byCode = TicketShowStatusEnum.getByCode(req.getShowFlag());
                    if (byCode != null){
                        req.setStatus(byCode.getStatus());
                        req.setExpiredFlag(byCode.getExpiredFlag());
                    }
                    req.setTicketCategoryList(StringUtils.isBlank(req.getTicketCategory()) ? null : Arrays.asList(req.getTicketCategory().split(",")));
                    req.setTicketCategory(null);
                    if (StringUtils.isBlank(req.getPositionName()) || CollectionUtil.isEmpty(list)){
                        return req;
                    }
                    List<String> positions = list.stream().map(HdsDevicePositionEntity::getPositionCode).toList();
                    req.setPositionCodes(positions);
                    return req;
                })
                .flatMap(this::findPageWithDevicePositions);
    }

    private Mono<PageResult<TicketAdminListResp>> findPageWithDevicePositions(TicketAdminPageReq req) {
        log.info("Get ticket page req: {}", JacksonUtils.writeValueAsString(req));
        return  Mono.zip(hdsServiceTicketDao.countByReq(req),hdsServiceTicketDao.findPageList(req))
                .flatMap(tuple -> {
                    Long total = tuple.getT1();
                    List<HdsServiceTicketEntity> entities = tuple.getT2();
                    Set<String> positions = entities.stream().map(HdsServiceTicketEntity::getPositionCode).collect(Collectors.toSet());
                    Set<String> hotelCodes = entities.stream().map(HdsServiceTicketEntity::getHotelCode).collect(Collectors.toSet());
                    return Mono.zip(serviceTicketService.getWechatUserInfoMap(entities), serviceTicketService.getAppUserInfoMap(entities)
                                    ,positionWithDeviceDao.getPositionList(positions), hdsHotelInfoDao.findList(hotelCodes))
                            .flatMap(tuple3-> {
                                Map<String, HdsChatUserEntity> wechatUserMap = tuple3.getT1();
                                Map<String, UserEntity> appUserMap = tuple3.getT2();
                                List<HdsDevicePositionEntity> positionEntities = tuple3.getT3();
                                Map<String, String> hotelInfoMap = tuple3.getT4().stream().collect(Collectors.toMap(HdsHotelInfoEntity::getHotelCode,HdsHotelInfoEntity::getHotelName, (v1,v2)->v1));

                                Map<String, HdsDevicePositionEntity> positionMap = positionEntities.stream().collect(Collectors.toMap(HdsDevicePositionEntity::getPositionCode, item -> item));
                                List<TicketAdminListResp> page = entities.stream()
                                        .map(ele-> buildTicketResp(ele,positionMap.get(ele.getPositionCode()),wechatUserMap,appUserMap,hotelInfoMap.get(ele.getHotelCode())))
                                        .toList();
                                return Mono.just(PageResult.create(total, page,req.getCurrent(),req.getPageSize()));
                            });
                });
    }

    private TicketAdminListResp buildTicketResp(HdsServiceTicketEntity ele, HdsDevicePositionEntity position, Map<String, HdsChatUserEntity> wechatUserMap, Map<String, UserEntity> appUserMap, String hotelName) {
        TicketAdminListResp resp = new TicketAdminListResp();
        BeanUtils.copyProperties(ele, resp);
        resp.setHotelName(hotelName);
        resp.setTicketCategory(ele.getServiceCategory());
        resp.setShowFlag(TicketShowStatusEnum.getShowFlag(ele.getExpiredFlag(),ele.getStatus()));
        resp.setTicketCategoryName(ServiceCategory.getChineseNameByCode(ele.getServiceCategory()));
        resp.setPositionName(position == null ? "" : position.getPositionName());
        resp.setHandleMethod(ele.getClosedLoopLevel());
        Optional.ofNullable(TicketClosedLoopLevel.getByCode(ele.getClosedLoopLevel())).ifPresent(e -> resp.setHandleMethodDesc(e.getConcept()));
        resp.setHandleMethodDesc(TicketHandleMethodEnum.getDescByTicket(ele));
        resp.setRtcRoomId(ele.getRtcRoomId());
        resp.setCreatedAt(DateUtil.format(ele.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
        Optional<String> appUserIf = Optional.ofNullable(appUserMap.get(ele.getDeviceId())).map(e -> e.getAreaCode() + "-" + e.getPhone());
        if (Objects.equals(ele.getClientType(), ClientType.MINI_PROGRAM.getCode())) {
            Optional.ofNullable(wechatUserMap.get(ele.getDeviceId())).map(e -> "86-" + e.getMobile()).ifPresent(phone -> resp.setCreatedByName("小程序-" + phone));
        } else {
            if (appUserIf.isPresent()) {
                resp.setCreatedByName("APP-" + appUserIf.get());
            } else {
                resp.setCreatedByName("客房Pad");
            }
        }
        resp.setCompletedAt(DateUtil.format(ele.getCompletionTime(), "yyyy-MM-dd HH:mm:ss"));
        if (ObjectUtil.equal(ele.getClosedLoopLevel(),TicketClosedLoopLevel.L1.getCode())){
            resp.setCompletedByName("AI");
        } else {
            resp.setCompletedByName(ele.getCompletedByName());
        }
        resp.setPositionName(DeviceUtils.getPositionFullName(position));
        return resp;
    }

    public Mono<TicketDetailResp> getTicketDetail(TicketInfoReq req) {
        log.info("getTicketDetail req {}", JacksonUtils.writeValueAsString(req));
        return hdsServiceTicketDao.findOneTicket(req)
                .flatMap(entity ->{
                    if (entity == null){
                        return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "工单不存在"));
                    }
                    return Mono.just(entity);
                })
                .flatMap(this::processTicketInfo);
    }

    private Mono<TicketDetailResp> processTicketInfo(HdsServiceTicketEntity ticket) {
        return Mono.zip(hdsTicketLogsDao.getLogsByTicketNo(ticket.getTicketNo()),
                        positionWithDeviceDao.getOnePosition(ticket.getPositionCode()),
                        serviceTicketService.getAppUserInfoMap(Collections.singletonList(ticket)),
                        serviceTicketService.getWechatUserInfoMap(Collections.singletonList(ticket)),
                        hdsCallLogDao.findList(GetCallLogQO.builder().conversationId(ticket.getConversationId()).rtcRoomId(ticket.getRtcRoomId()).hotelCode(ticket.getHotelCode()).build(),null),
                        hdsCallFeedbackDao.selectByRtcRoomId(ticket.getReturnRoomId()),
                        hdsHotelInfoDao.findList(Sets.newHashSet(ticket.getHotelCode()))
                )
                .map(tuple -> {
                    log.info("getTicketDetail ticket {}", JacksonUtils.writeValueAsString(ticket));
                    List<HdsTicketLogsEntity> logs = tuple.getT1();
                    HdsDevicePositionEntity position = tuple.getT2();
                    Map<String, UserEntity> appUserMap = tuple.getT3();
                    Map<String, HdsChatUserEntity> wechatUserMap = tuple.getT4();
                    TicketDetailResp resp = new TicketDetailResp();
                    String hotelName = CollectionUtil.isEmpty(tuple.getT7()) ? "" : tuple.getT7().get(0).getHotelName();
                    TicketAdminListResp ticketResp = buildTicketResp(ticket, position, wechatUserMap,appUserMap, hotelName);
                    resp.setTicketInfo(ticketResp);
                    List<TicketLogResp> operateLog = new ArrayList<>();
                    List<HdsCallFeedbackEntity> t6 = tuple.getT6();
                    if (CollectionUtil.isNotEmpty(t6)){
                        HdsCallFeedbackEntity feedback = t6.get(0);
                        TicketLogResp feedbackLog = new TicketLogResp();
                        feedbackLog.setActionType("客人反馈 "+   FeedbackStatus.getByCode(feedback.getFeedbackStatus()).getDescription());
                        feedbackLog.setCreatedAt(DateUtil.format(feedback.getFeedbackTime(), "yyyy-MM-dd HH:mm:ss"));
                        operateLog.add(feedbackLog);
                    }

                    for (HdsTicketLogsEntity logsEntity : logs) {
                        TicketLogResp logResp = new TicketLogResp();
                        BeanUtils.copyProperties(logsEntity, logResp);
                        logResp.setCreatedAt(DateUtil.format(logsEntity.getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
                        logResp.setActionType(TicketActionTypeEnum.getDescByCode(logsEntity.getActionType()));
                        if (logsEntity.getActionType().equals(TicketActionTypeEnum.CREATE.getCode())){
                            logResp.setCreatedByName(ticketResp.getCreatedByName());
                        }else if (logsEntity.getActionType().equals(TicketActionTypeEnum.COMPLETE.getCode())){
                            logResp.setCreatedByName(ticketResp.getCompletedByName());
                            logResp.setActionType("处理完成（"+TicketClosedLoopLevel.getByCode(ticket.getClosedLoopLevel()).getConcept()+")");
                        }
                        operateLog.add(logResp);
                    }

                    // 发起对话
                    TicketLogResp startLog = new TicketLogResp();
                    startLog.setActionType(StringUtils.isBlank(ticket.getRtcRoomId()) && ObjectUtil.notEqual(ticket.getServiceSubcategory(), ServiceSubcategory.EM_SOS.getCode()) ? "客人发起文字对话" :"客人发起呼叫请求");
                    List<HdsCallLogEntity> t5 = tuple.getT5();
                    if (CollectionUtil.isNotEmpty(t5)){
                        startLog.setCreatedAt(DateUtil.format(t5.get(0).getCreatedAt(), "yyyy-MM-dd HH:mm:ss"));
                    }
                    List<TicketLogResp.TicketConservationRecordResp> records = t5.stream().map(item->{
                        TicketLogResp.TicketConservationRecordResp record = new TicketLogResp.TicketConservationRecordResp();
                        if(ObjectUtil.equal(item.getCallType(), YesNoEnum.YES.getCode())){
                            // 点对点
                            DeviceTypeEnum byPrefix = DeviceTypeEnum.getByPrefix(item.getCreatedBy());
                            record.setName(byPrefix == null ? "-" : (DeviceTypeEnum.judgeDeviceFromHotel(byPrefix.getCode()) ? "前台":"客人"));
                        } else {
                            record.setName(ObjectUtil.equal(item.getMessageType(),"user") ? "客人": "AI" );
                        }
                        record.setContent(item.getContent());
                        return record;
                    }).collect(Collectors.toList());
                    startLog.setConservationRecords(records);
                    operateLog.add(startLog);
                    resp.setOperateLog(operateLog);
                    log.info("getTicketDetail resp {}", JacksonUtils.writeValueAsString(resp));
                    return resp;
                });
    }

    public Mono<List<FocusTicketResp>> getTopNFocusTickets(GetTopNTicketReq req) {
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getHotelCode()), "酒店编码不能为空");
        Preconditions.checkArgument(ObjectUtil.isNotNull(req.getStatus()), "筛选状态不能为空");
        return hdsServiceTicketDao.getTopNFocusTickets(req)
                .flatMap(tickets->{
                    if (CollectionUtil.isEmpty(tickets)){
                        return Mono.just(new ArrayList<>());
                    }
                    Set<String> positions = tickets.stream().map(HdsServiceTicketEntity::getPositionCode).collect(Collectors.toSet());
                    return positionWithDeviceDao.getPositionList(positions)
                            .map(positionsWithDevices -> {
                                Map<String, HdsDevicePositionEntity> positionMap = positionsWithDevices.stream().collect(Collectors.toMap(HdsDevicePositionEntity::getPositionCode, item -> item));
                                return tickets.stream().map(ticket -> {
                                    FocusTicketResp resp = new FocusTicketResp();
                                    BeanUtils.copyProperties(ticket, resp);
                                    HdsDevicePositionEntity position = positionMap.get(ticket.getPositionCode());
                                    resp.setPositionFullName(DeviceUtils.getPositionFullName(position));
                                    resp.setTicketId(ticket.getId());
                                    resp.setServiceCategoryShow(ServiceCategory.getByCode(ticket.getServiceCategory(),false).getDisplayText());
                                    resp.setFocusTagColor(FocusTagEnum.getFocusTagColor(ticket));
                                    resp.setWordColor(ObjectUtil.equal(resp.getFocusTagColor(),ColorEnum.YELLOW.getHexCode()) ? ColorEnum.BLACK.getHexCode() : ColorEnum.WHITE.getHexCode());
                                    resp.setFocusTagText(FocusTagEnum.getExtraTagShow(ticket));
                                    resp.setOverdueFlag(ticket.getOverdueFlag());
                                    long minutesBetweenNow = DateUtils.minutesBetweenNow(ticket.getCreatedAt());
                                    resp.setWaitMinutes((int)minutesBetweenNow);
                                    String waitMinutes = minutesBetweenNow < 1 ? "刚刚" :"等待" + minutesBetweenNow + "分钟";
                                    resp.setWaitMinutesDesc(waitMinutes);
                                    resp.setCompletedTime(DateUtil.format(ticket.getCompletionTime(),"yyyy-MM-dd HH:mm:ss"));
                                    resp.setCreatedAt(DateUtil.format(ticket.getCreatedAt(),"yyyy-MM-dd HH:mm:ss"));
                                    return resp;
                                }).collect(Collectors.toList());
                            });
                });
    }

    /**
     * 工单每日统计查询（Redis Hash结构缓存，字段名与DailyTicketsResp一致，完成率动态计算）
     */
    public Mono<DailyTicketsResp> getDailyTickets(GetDailyTicketsReq req) {
        String hotelCode = req.getHotelCode();
        String dateStr = req.getBizDate();
        String bizDate = StringUtils.isBlank(dateStr) ? DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN) : dateStr;
        String cacheKey = String.format(RedisConstant.HOTEL_DAILY_TICKET_STAT_KEY, hotelCode, bizDate);

        // 1. 查询Redis Hash
        // 先判断 key 是否存在并打印
        return reactiveStringRedisTemplate.hasKey(cacheKey)
                .flatMap(exists -> {
                    if (!exists) {
                        log.warn("Redis key 不存在: {}", cacheKey);
                        return Mono.just(new DailyTicketsResp());
                    }
                    // key 存在再查 hash
                    return reactiveStringRedisTemplate.<String, String>opsForHash().entries(cacheKey)
                            .collectMap(Map.Entry::getKey, Map.Entry::getValue)
                            .flatMap(map -> {
                                if (MapUtils.isNotEmpty(map)) {
                                    DailyTicketsResp resp = buildDailyTicketsResp(map);
                                    handleStatPercent(resp);
                                    return Mono.just(resp);
                                }
                                return Mono.just(new DailyTicketsResp());
                            });
                });
    }

    private DailyTicketsResp buildDailyTicketsResp(Map<String, String> map) {
        DailyTicketsResp resp = new DailyTicketsResp();
        resp.setAiCallCount(map.get(RedisConstant.DailyTicketField.AI_CALL_COUNT));
        resp.setReturnCallCount(map.get(RedisConstant.DailyTicketField.RETURN_CALL_COUNT));
        resp.setTicketCount(map.get(RedisConstant.DailyTicketField.TICKET_COUNT));
        resp.setCompletedTicketCount(map.get(RedisConstant.DailyTicketField.COMPLETED_TICKET_COUNT));
        resp.setInquiryCount(map.get(RedisConstant.DailyTicketField.INQUIRY_COUNT));
        resp.setInquiryCompletedCount(map.get(RedisConstant.DailyTicketField.INQUIRY_COMPLETED_COUNT));
        resp.setServiceNeedCount(map.get(RedisConstant.DailyTicketField.SERVICE_NEED_COUNT));
        resp.setServiceNeedCompletedCount(map.get(RedisConstant.DailyTicketField.SERVICE_NEED_COMPLETED_COUNT));
        resp.setComplaintCount(map.get(RedisConstant.DailyTicketField.COMPLAINT_COUNT));
        resp.setComplaintCompletedCount(map.get(RedisConstant.DailyTicketField.COMPLAINT_COMPLETED_COUNT));
        resp.setEmergencyCount(map.get(RedisConstant.DailyTicketField.EMERGENCY_COUNT));
        resp.setEmergencyCompletedCount(map.get(RedisConstant.DailyTicketField.EMERGENCY_COMPLETED_COUNT));
        return resp;
    }

    private void handleStatPercent(DailyTicketsResp stat) {
        stat.setCompletedTicketPercent(calcPercent(stat.getCompletedTicketCount(), stat.getTicketCount()));
        stat.setInquiryCompletedPercent(calcPercent(stat.getInquiryCompletedCount(), stat.getInquiryCount()));
        stat.setServiceNeedCompletedPercent(calcPercent(stat.getServiceNeedCompletedCount(), stat.getServiceNeedCount()));
        stat.setComplaintCompletedPercent(calcPercent(stat.getComplaintCompletedCount(), stat.getComplaintCount()));
        stat.setEmergencyCompletedPercent(calcPercent(stat.getEmergencyCompletedCount(), stat.getEmergencyCount()));
    }

    private Map<String, String> buildRedisMap(DailyTicketsResp stat) {
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put(RedisConstant.DailyTicketField.AI_CALL_COUNT, String.valueOf(stat.getAiCallCount()));
        hashMap.put(RedisConstant.DailyTicketField.RETURN_CALL_COUNT, String.valueOf(stat.getReturnCallCount()));
        hashMap.put(RedisConstant.DailyTicketField.TICKET_COUNT, String.valueOf(stat.getTicketCount()));
        hashMap.put(RedisConstant.DailyTicketField.COMPLETED_TICKET_COUNT, String.valueOf(stat.getCompletedTicketCount()));
        hashMap.put(RedisConstant.DailyTicketField.INQUIRY_COUNT, String.valueOf(stat.getInquiryCount()));
        hashMap.put(RedisConstant.DailyTicketField.INQUIRY_COMPLETED_COUNT, String.valueOf(stat.getInquiryCompletedCount()));
        hashMap.put(RedisConstant.DailyTicketField.SERVICE_NEED_COUNT, String.valueOf(stat.getServiceNeedCount()));
        hashMap.put(RedisConstant.DailyTicketField.SERVICE_NEED_COMPLETED_COUNT, String.valueOf(stat.getServiceNeedCompletedCount()));
        hashMap.put(RedisConstant.DailyTicketField.COMPLAINT_COUNT, String.valueOf(stat.getComplaintCount()));
        hashMap.put(RedisConstant.DailyTicketField.COMPLAINT_COMPLETED_COUNT, String.valueOf(stat.getComplaintCompletedCount()));
        hashMap.put(RedisConstant.DailyTicketField.EMERGENCY_COUNT, String.valueOf(stat.getEmergencyCount()));
        hashMap.put(RedisConstant.DailyTicketField.EMERGENCY_COMPLETED_COUNT, String.valueOf(stat.getEmergencyCompletedCount()));
        return hashMap;
    }

    /**
     * 字符串转int，空或异常时返回0
     */
    private int parseInt(String v) {
        try { return StringUtils.isEmpty(v) ? 0 : Integer.parseInt(v); } catch (Exception e) { return 0; }
    }

    /**
     * 计算完成率百分比（分母为0时返回100）
     */
    private String calcPercent(String completed, String total) {
        if (StringUtils.isEmpty(total) || parseInt(total) == 0) return "100";
        int percent = (int) Math.round(parseInt(completed) * 100.0 / parseInt(total));
        return String.valueOf(Math.min(percent, 100));
    }

    /**
     * 统计指定酒店、指定日期的每日工单各类数量
     */
    private Mono<DailyTicketsResp> statTicketsFromDb(String hotelCode, LocalDate queryDate) {
        LocalDateTime start = queryDate.atStartOfDay();
        LocalDateTime end = queryDate.plusDays(1).atStartOfDay();
        HdsTicketQO qo = HdsTicketQO.builder()
            .hotelCode(hotelCode)
            .start(start)
            .end(end)
            .build();
        return hdsServiceTicketDao.findList(qo)
            .map(tickets -> {
                DailyTicketsResp resp = new DailyTicketsResp();
                // AI通话、回拨通话数量如需统计请补充业务逻辑
                resp.setAiCallCount("0"); // TODO: AI通话统计
                resp.setReturnCallCount("0"); // TODO: 回拨通话统计
                resp.setTicketCount(String.valueOf(tickets.size()));
                resp.setCompletedTicketCount(String.valueOf(tickets.stream().filter(t -> Objects.equals(t.getStatus(), TicketStatus.COMPLETED.getCode())).count()));
                // 问询
                resp.setInquiryCount(String.valueOf(tickets.stream().filter(t -> ServiceType.INQUIRY.name().equals(t.getServiceType())).count()));
                resp.setInquiryCompletedCount(String.valueOf(tickets.stream().filter(t -> ServiceType.INQUIRY.name().equals(t.getServiceType()) && Objects.equals(t.getStatus(), TicketStatus.COMPLETED.getCode())).count()));
                // 客需 todo
                // 客诉
                resp.setComplaintCount(String.valueOf(tickets.stream().filter(t -> ServiceCategory.COMPLAINT.getCode().equals(t.getServiceCategory())).count()));
                resp.setComplaintCompletedCount(String.valueOf(tickets.stream().filter(t -> ServiceCategory.COMPLAINT.getCode().equals(t.getServiceCategory()) && Objects.equals(t.getStatus(), TicketStatus.COMPLETED.getCode())).count()));
                // 紧急
                resp.setEmergencyCount(String.valueOf(tickets.stream().filter(t -> ServiceCategory.EMERGENCY.getCode().equals(t.getServiceCategory())).count()));
                resp.setEmergencyCompletedCount(String.valueOf(tickets.stream().filter(t -> ServiceCategory.EMERGENCY.getCode().equals(t.getServiceCategory()) && Objects.equals(t.getStatus(), TicketStatus.COMPLETED.getCode())).count()));
                return resp;
            });
    }

    /**
     * 查询工单统计（异常/正常）
     *
     * @param req 请求参数
     * @return 工单统计结果
     */
    public Mono<List<TicketStatisticsResp>> getTicketStatistics(TicketStatisticsReq req) {
        log.info("getTicketStatistics req: {}", JacksonUtils.writeValueAsString(req));

        // 参数校验
        if (StringUtils.isBlank(req.getSearchDate())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "查询日期不能为空"));
        }

        LocalDate searchDate;
        try {
            searchDate = LocalDate.parse(req.getSearchDate());
        } catch (Exception e) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "日期格式错误，请使用 yyyy-MM-dd 格式"));
        }

        // 并行查询所有营业中的酒店、异常工单和正常工单统计
        return Mono.zip(
                hdsHotelInfoDao.findAllActiveHotels(),
                hdsServiceTicketDao.getAbnormalTicketStatistics(searchDate),
                hdsServiceTicketDao.getNormalTicketStatistics(searchDate)
        ).flatMap(tuple -> {
            List<HdsHotelInfoEntity> allActiveHotels = tuple.getT1();
            List<TicketStatisticsDTO> abnormalList = tuple.getT2();
            List<TicketStatisticsDTO> normalList = tuple.getT3();

            if (allActiveHotels.isEmpty()) {
                return Mono.just(new ArrayList<>());
            }

            Map<String, Integer> abnormalCountMap = abnormalList.stream()
                    .collect(Collectors.toMap(
                            TicketStatisticsDTO::getHotelCode,
                            TicketStatisticsDTO::getCount,
                            (c1, c2) -> c1
                    ));

            Map<String, Integer> normalCountMap = normalList.stream()
                    .collect(Collectors.toMap(
                            TicketStatisticsDTO::getHotelCode,
                            TicketStatisticsDTO::getCount,
                            (c1, c2) -> c1
                    ));

            // 组装响应数据
            List<TicketStatisticsResp> result = new ArrayList<>();
            for (HdsHotelInfoEntity hotel : allActiveHotels) {
                TicketStatisticsResp resp = new TicketStatisticsResp();
                resp.setHotelCode(hotel.getHotelCode());
                resp.setHotelName(hotel.getHotelName());
                resp.setAbnormalCount(abnormalCountMap.getOrDefault(hotel.getHotelCode(), 0));
                resp.setNormalCount(normalCountMap.getOrDefault(hotel.getHotelCode(), 0));
                result.add(resp);
            }

            // 按异常工单数量、正常工单数量排序
            result.sort(Comparator.comparing(TicketStatisticsResp::getAbnormalCount, Comparator.reverseOrder())
                    .thenComparing(TicketStatisticsResp::getNormalCount, Comparator.reverseOrder()));

            log.info("getTicketStatistics result size: {}", result.size());
            return Mono.just(result);
        });
    }
}
