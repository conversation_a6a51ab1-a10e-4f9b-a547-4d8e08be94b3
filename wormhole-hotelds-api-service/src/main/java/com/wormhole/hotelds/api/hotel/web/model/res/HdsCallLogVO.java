package com.wormhole.hotelds.api.hotel.web.model.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/4/17
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HdsCallLogVO implements Serializable {


    /**
     * 主键ID
     */
    private Long id;


    /**
     * 关联的聊天消息ID
     */
    private String messageId;


    /**
     * 消息类型user、assistant、system
     */
    private String messageType;


    /**
     * 会话ID
     */
    private String conversationId;


    /**
     * RTC语音房间ID
     */
    private String rtcRoomId;


    /**
     * 设备标识
     */
    private String deviceId;


    /**
     * 客户端请求ID
     */
    private String clientReqId;


    /**
     * 智能体code
     */
    private String botCode;


    /**
     * 消息内容
     */
    private String content;

    /**
     * 通话类型: 0-智能体通话 1-人工点对点通话
     */
    private Integer callType;


    /**
     * 服务类型: SERVICE-服务工单 INQUIRY-问询
     */
    private String serviceType;


    /**
     * 服务分类: DELIVERY-送物 CLEANING-清洁 TOURISM-周边旅游等
     */
    private String serviceCategory;


    /**
     * 服务子分类
     */
    private String serviceSubcategory;


    /**
     * 服务类别名称
     */
    private String serviceCategoryName;


    /**
     * 服务子类别名称
     */
    private String serviceSubcategoryName;


    /**
     * 关联工单编号
     */
    private String relatedTicketNo;


    /**
     * 酒店代码
     */
    private String hotelCode;


    /**
     * 房间编码
     */
    private String positionCode;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    private Long createdAtTimeStamp;

    private String botName;


}
